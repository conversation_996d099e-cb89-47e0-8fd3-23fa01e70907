<script setup lang="ts">
import { ref } from 'vue'
import type { Question } from '../utils/questionGenerator'
import { renderMarkdown } from '../utils/markdownRenderer'

interface Props {
  question: Question
}

const props = defineProps<Props>()

const isFavorited = ref(false)
const isHovered = ref(false)
const mouseX = ref(0)
const mouseY = ref(0)

const handleMouseMove = (event: MouseEvent) => {
  const card = event.currentTarget as HTMLElement
  const rect = card.getBoundingClientRect()
  mouseX.value = event.clientX - rect.left
  mouseY.value = event.clientY - rect.top
}

const categoryStyles = {
  critical: 'bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 border-red-100 dark:border-red-800',
  creative: 'bg-purple-50 dark:bg-purple-900/20 text-purple-700 dark:text-purple-300 border-purple-100 dark:border-purple-800',
  logical: 'bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 border-blue-100 dark:border-blue-800',
  system: 'bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300 border-green-100 dark:border-green-800',
  ethical: 'bg-orange-50 dark:bg-orange-900/20 text-orange-700 dark:text-orange-300 border-orange-100 dark:border-orange-800',
  practical: 'bg-teal-50 dark:bg-teal-900/20 text-teal-700 dark:text-teal-300 border-teal-100 dark:border-teal-800'
}

const categoryLabels = {
  critical: '批判性思维',
  creative: '创意思维',
  logical: '逻辑分析',
  system: '系统思维',
  ethical: '伦理思考',
  practical: '实践应用'
}

const difficultyStyles = {
  beginner: 'bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300 border-green-100 dark:border-green-800',
  intermediate: 'bg-yellow-50 dark:bg-yellow-900/20 text-yellow-700 dark:text-yellow-300 border-yellow-100 dark:border-yellow-800',
  advanced: 'bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 border-red-100 dark:border-red-800'
}

const difficultyLabels = {
  beginner: '入门',
  intermediate: '中级',
  advanced: '高级'
}

const toggleFavorite = () => {
  isFavorited.value = !isFavorited.value
}

const startSystemicAnalysis = () => {
  // 打开系统分析页面，但不自动开始分析
  const analysisUrl = `/analysis?topic=${encodeURIComponent(props.question.question)}`
  window.open(analysisUrl, '_blank', 'width=1400,height=900,scrollbars=yes,resizable=yes')
}

const startRoundtableMeeting = () => {
  // 打开圆桌会议页面，传递问题作为主题和提示
  const roundtableUrl = `/roundtable?topic=${encodeURIComponent(props.question.question)}&hints=${encodeURIComponent(JSON.stringify(props.question.hints))}`
  window.open(roundtableUrl, '_blank', 'width=1400,height=900,scrollbars=yes,resizable=yes')
}
</script>

<template>
  <div 
    :class="[
      'question-card group relative overflow-hidden bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-700',
      { 'backdrop-blur-sm': isHovered } // Add backdrop blur to the card itself
    ]"
    @mouseenter="isHovered = true"
    @mouseleave="isHovered = false"
    @mousemove="handleMouseMove"
  >
    <!-- 悬停时的背景渐变 -->
    <div 
      :class="[
        'absolute inset-0 transition-opacity duration-300',
        { 'opacity-100': isHovered }
      ]"
      :style="{
        background: `radial-gradient(circle at ${mouseX}px ${mouseY}px, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 70%)`,
        '--tw-gradient-from': 'rgba(255,255,255,0.2)',
        '--tw-gradient-to': 'rgba(255,255,255,0)',
        '--tw-gradient-stops': 'var(--tw-gradient-from), var(--tw-gradient-to)',
        'background-image': `radial-gradient(circle at ${mouseX}px ${mouseY}px, var(--tw-gradient-stops))`
      }"
    ></div>
    
    <div class="relative z-10">
      <!-- 卡片头部 -->
      <div class="flex items-start justify-between mb-3 sm:mb-4">
        <div class="flex flex-wrap gap-1.5 sm:gap-2 flex-1 mr-2">
          <span :class="['category-badge border text-xs sm:text-sm', categoryStyles[question.category]]">
            {{ categoryLabels[question.category] }}
          </span>
          <span :class="['category-badge border text-xs sm:text-sm', difficultyStyles[question.difficulty]]">
            {{ difficultyLabels[question.difficulty] }}
          </span>
        </div>
        <button
          @click="toggleFavorite"
          :class="[
            'p-1.5 sm:p-2 rounded-full transition-all duration-200 transform hover:scale-110 flex-shrink-0',
            isFavorited 
              ? 'text-red-500 bg-red-50 dark:bg-red-900/20 hover:bg-red-100 dark:hover:bg-red-900/30' 
              : 'text-slate-400 dark:text-slate-500 hover:text-red-500 hover:bg-red-50 dark:hover:bg-red-900/20'
          ]"
        >
          <svg class="h-4 w-4 sm:h-5 sm:w-5" fill="currentColor" viewBox="0 0 20 20">
            <path
              :fill-rule="isFavorited ? 'evenodd' : 'nonzero'"
              d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z"
              :clip-rule="isFavorited ? 'evenodd' : 'nonzero'"
            />
          </svg>
        </button>
      </div>

      <!-- 问题内容 -->
      <div class="mb-4 sm:mb-6">
        <h4 class="text-base sm:text-lg font-semibold text-slate-800 dark:text-slate-200 mb-2 sm:mb-3 leading-tight">
          {{ question.question }}
        </h4>
        <p class="text-slate-600 dark:text-slate-400 text-sm leading-relaxed"
          v-html="renderMarkdown(question.description)">
        </p>
      </div>

      <!-- 提示信息 -->
      <div v-if="question.hints.length > 0" class="mb-4 sm:mb-6">
        <h5 class="text-sm font-medium text-slate-700 dark:text-slate-300 mb-2 sm:mb-3 flex items-center">
          <svg class="h-3 w-3 sm:h-4 sm:w-4 mr-1 text-blue-500 dark:text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
          </svg>
          思考提示
        </h5>
        <ul class="space-y-1.5 sm:space-y-2">
          <li
            v-for="(hint, index) in question.hints"
            :key="hint"
            class="text-xs sm:text-sm text-slate-600 dark:text-slate-400 flex items-start animate-slide-up"
            :style="{ 'animation-delay': `${index * 0.1}s` }"
          >
            <span class="text-blue-500 dark:text-blue-400 mr-2 mt-0.5 font-medium">•</span>
            <span class="flex-1" v-html="renderMarkdown(hint)"></span>
          </li>
        </ul>
      </div>

      <!-- 卡片底部 -->
      <div class="flex items-center justify-between pt-3 sm:pt-4 border-t border-slate-100 dark:border-slate-700">
        <div class="flex items-center text-xs sm:text-sm text-slate-500 dark:text-slate-400">
          <svg class="h-3 w-3 sm:h-4 sm:w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          {{ question.estimatedTime }}
        </div>
        <div class="flex items-center space-x-2">
          <button
            @click="startSystemicAnalysis"
            class="group/btn flex items-center space-x-1.5 sm:space-x-2 text-xs sm:text-sm font-medium text-purple-600 dark:text-purple-400 hover:text-purple-700 dark:hover:text-purple-300 bg-purple-50 dark:bg-purple-900/20 hover:bg-purple-100 dark:hover:bg-purple-900/30 px-3 sm:px-4 py-1.5 sm:py-2 rounded-lg transition-all duration-200 transform hover:scale-105 active:scale-95"
          >
            <span>系统分析</span>
            <svg class="h-3 w-3 sm:h-4 sm:w-4 transition-transform duration-200 group-hover/btn:translate-x-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6" />
            </svg>
          </button>
          <button
            @click="startRoundtableMeeting"
            class="group/btn flex items-center space-x-1.5 sm:space-x-2 text-xs sm:text-sm font-medium text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 bg-blue-50 dark:bg-blue-900/20 hover:bg-blue-100 dark:hover:bg-blue-900/30 px-3 sm:px-4 py-1.5 sm:py-2 rounded-lg transition-all duration-200 transform hover:scale-105 active:scale-95"
          >
            <span>圆桌会议</span>
            <svg class="h-3 w-3 sm:h-4 sm:w-4 transition-transform duration-200 group-hover/btn:translate-x-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.714-4.03 8.5-9 8.5S3 16.714 3 12c0-4.714 4.03-8.5 9-8.5s9 3.786 9 8.5z" />
            </svg>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.category-badge {
  @apply inline-flex items-center px-2 py-1 rounded-full font-medium;
}

@media (max-width: 640px) {
  .category-badge {
    @apply px-1.5 py-0.5;
  }
}
</style>