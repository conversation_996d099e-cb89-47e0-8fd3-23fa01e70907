<script setup lang="ts">
import { computed } from 'vue'
import { language } from '../stores/language'
import { theme, toggleTheme } from '../stores/theme'

const getThemeIcon = () => {
  switch (theme.themePreference.value) {
    case 'light':
      return '☀️'
    case 'dark':
      return '🌙'
    case 'system':
      return '🌓'
    default:
      return '🌓'
  }
}

const getThemeLabel = computed(() => {
  if (language.value === 'zh') {
    switch (theme.themePreference.value) {
      case 'light':
        return '浅色模式'
      case 'dark':
        return '深色模式'
      case 'system':
        return '跟随系统'
      default:
        return '跟随系统'
    }
  } else {
    switch (theme.themePreference.value) {
      case 'light':
        return 'Light Mode'
      case 'dark':
        return 'Dark Mode'
      case 'system':
        return 'System'
      default:
        return 'System'
    }
  }
})
</script>

<template>
  <button
    @click="toggleTheme"
    class="flex items-center space-x-1.5 sm:space-x-2 px-2 sm:px-4 py-1.5 sm:py-2 rounded-lg hover:bg-slate-100/80 dark:hover:bg-slate-800/80 transition-all duration-200 text-slate-600 dark:text-slate-300 hover:text-slate-800 dark:hover:text-slate-100 transform hover:scale-105"
    :title="getThemeLabel"
  >
    <span class="text-base sm:text-lg">{{ getThemeIcon() }}</span>
    <span class="text-xs sm:text-sm font-medium hidden sm:inline">{{ getThemeLabel }}</span>
  </button>
</template>