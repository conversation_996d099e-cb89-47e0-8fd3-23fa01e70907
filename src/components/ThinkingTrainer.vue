<script setup lang="ts">
import { ref, computed, onMounted, shallowRef } from 'vue'
import { useRouter } from 'vue-router'
import { language } from '../stores/language'
import QuestionCard from './QuestionCard.vue'
import ModelSettings from './ModelSettings.vue'
import HistoryPanel from './HistoryPanel.vue'
import ThemeToggle from './ThemeToggle.vue'
import LanguageToggle from './LanguageToggle.vue'

import { generateQuestions, type Question, type QuestionCategory } from '../utils/questionGenerator'
import { GeminiQuestionGenerator, getActiveModelConfig } from '../utils/geminiApi'
import { isLoading, startLoading, stopLoading } from '../utils/loadingState'

const inputText = ref('')
const questions = shallowRef<Question[]>([])
const selectedCategory = ref<QuestionCategory | 'all'>('all')
const error = ref('')
const showHistory = ref(false)
const inputFocused = ref(false)

// 历史记录
interface HistoryRecord {
  id: string
  topic: string
  questions: Question[]
  timestamp: number
}

const history = shallowRef<HistoryRecord[]>([])

// 性能优化：使用计算属性缓存
const filteredQuestions = computed(() => {
  if (selectedCategory.value === 'all') {
    return questions.value
  }
  return questions.value.filter(q => q.category === selectedCategory.value)
})

const hasQuestions = computed(() => questions.value.length > 0)

const hasActiveModel = computed(() => {
  const activeModel = getActiveModelConfig()
  return activeModel?.apiKey ? true : false
})

const translations = computed(() => {
  return {
    searchInputPlaceholder: language.value === 'zh' ? '输入任何实体、概念或主题...' : 'Enter any entity, concept, or topic...',
    history: language.value === 'zh' ? '历史' : 'History',
    categories: language.value === 'zh' ?
      [
        { value: 'all' as const, label: '全部', color: 'bg-slate-100 dark:bg-slate-700 text-slate-700 dark:text-slate-300' },
        { value: 'critical' as const, label: '批判性思维', color: 'bg-red-100 dark:bg-red-900/20 text-red-700 dark:text-red-300' },
        { value: 'creative' as const, label: '创意思维', color: 'bg-purple-100 dark:bg-purple-900/20 text-purple-700 dark:text-purple-300' },
        { value: 'logical' as const, label: '逻辑分析', color: 'bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300' },
        { value: 'system' as const, label: '系统思维', color: 'bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300' },
        { value: 'ethical' as const, label: '伦理思考', color: 'bg-orange-100 dark:bg-orange-900/20 text-orange-700 dark:text-orange-300' },
        { value: 'practical' as const, label: '实践应用', color: 'bg-teal-100 dark:bg-teal-900/20 text-teal-700 dark:text-teal-300' }
      ] :
      [
        { value: 'all' as const, label: 'All', color: 'bg-slate-100 dark:bg-slate-700 text-slate-700 dark:text-slate-300' },
        { value: 'critical' as const, label: 'Critical Thinking', color: 'bg-red-100 dark:bg-red-900/20 text-red-700 dark:text-red-300' },
        { value: 'creative' as const, label: 'Creative Thinking', color: 'bg-purple-100 dark:bg-purple-900/20 text-purple-700 dark:text-purple-300' },
        { value: 'logical' as const, label: 'Logical Analysis', color: 'bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300' },
        { value: 'system' as const, label: 'System Thinking', color: 'bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300' },
        { value: 'ethical' as const, label: 'Ethical Consideration', color: 'bg-orange-100 dark:bg-orange-900/20 text-orange-700 dark:text-orange-300' },
        { value: 'practical' as const, label: 'Practical Application', color: 'bg-teal-100 dark:bg-teal-900/20 text-teal-700 dark:text-teal-300' }
      ]
  }
})

// 分类数据优化：使用计算属性缓存
const categories = computed(() => translations.value.categories)


// 从localStorage加载历史记录
onMounted(() => {
  loadHistory()
  
  // 监听历史记录更新事件
  window.addEventListener('history-updated', loadHistory)
})

const loadHistory = () => {
  const savedHistory = localStorage.getItem('thinking-history')
  if (savedHistory) {
    try {
      history.value = JSON.parse(savedHistory)
    } catch (error) {
      console.error('Failed to load history:', error)
      history.value = []
    }
  }
}

const saveToHistory = (topic: string, generatedQuestions: Question[]) => {
  const record: HistoryRecord = {
    id: `history-${Date.now()}`,
    topic,
    questions: generatedQuestions,
    timestamp: Date.now()
  }
  
  history.value = [record, ...history.value]
  
  // 只保留最近50条记录
  if (history.value.length > 50) {
    history.value = history.value.slice(0, 50)
  }
  
  localStorage.setItem('thinking-history', JSON.stringify(history.value))
}

const handleGenerate = async () => {
  if (!inputText.value.trim()) return
  
  error.value = ''
  startLoading('Generating with AI...', hasActiveModel.value ? 1 : 0)
  
  try {
    let generatedQuestions
    
    // 获取当前激活的模型配置
    const activeModel = getActiveModelConfig()
    
    if (activeModel?.apiKey) {
      // 使用配置的 AI 模型生成问题
      const generator = new GeminiQuestionGenerator(activeModel.apiKey, activeModel)
      generatedQuestions = await generator.generateQuestions(inputText.value.trim())
    } else {
      // 使用本地模板生成问题
      await new Promise(resolve => setTimeout(resolve, 1200))
      generatedQuestions = generateQuestions(inputText.value.trim())
    }
    
    questions.value = generatedQuestions
    
    // 保存到历史记录
    saveToHistory(inputText.value.trim(), generatedQuestions)
    
  } catch (err) {
    console.error('Failed to generate questions:', err)
    error.value = err instanceof Error ? err.message : 'Unknown error occurred while generating questions'
    
    // 如果API失败，回退到本地生成
    const activeModel = getActiveModelConfig()
    if (activeModel?.apiKey) {
      try {
        await new Promise(resolve => setTimeout(resolve, 800))
        const fallbackQuestions = generateQuestions(inputText.value.trim())
        questions.value = fallbackQuestions
        saveToHistory(inputText.value.trim(), fallbackQuestions)
        error.value += ' (Generated questions using local template)'
      } catch (fallbackErr) {
        console.error('Local generation also failed:', fallbackErr)
      }
    }
  } finally {
    stopLoading()
  }
}

const handleClear = () => {
  inputText.value = ''
  questions.value = []
  selectedCategory.value = 'all'
  error.value = ''
}

const router = useRouter()

const navigateToRoundtable = () => {
  if (inputText.value.trim()) {
    router.push({ path: '/roundtable', query: { topic: inputText.value.trim() } })
  }
}

const loadFromHistory = (record: HistoryRecord) => {
  inputText.value = record.topic
  questions.value = [] // 先清空
  questions.value = [...record.questions] // 再赋值
  selectedCategory.value = 'all'
  showHistory.value = false
}

const toggleHistory = () => {
  showHistory.value = !showHistory.value
}

// 输入框焦点处理
const handleInputFocus = () => {
  inputFocused.value = true
}

const handleInputBlur = () => {
  inputFocused.value = false
}

// 性能优化：防抖处理分类切换
let categoryTimeout: number | null = null
const handleCategoryChange = (category: QuestionCategory | 'all') => {
  if (categoryTimeout) {
    clearTimeout(categoryTimeout)
  }
  categoryTimeout = setTimeout(() => {
    selectedCategory.value = category
  }, 50) as any
}

// 获取分类问题数量（缓存计算）
const getCategoryCount = (category: QuestionCategory | 'all') => {
  if (category === 'all') return questions.value.length
  return questions.value.filter(q => q.category === category).length
}
</script>

<template>
  <div class="bg-white dark:bg-slate-900 flex flex-col transition-colors duration-300 min-h-screen">
    <div class="flex-1 pb-16 sm:pb-20">
      <div class="container mx-auto px-3 sm:px-4 max-w-4xl">
        <!-- 居中的搜索区域 -->
        <div :class="[
          'transition-all duration-700 ease-out flex flex-col items-center',
          hasQuestions 
            ? 'pt-6 sm:pt-12 pb-6 sm:pb-8' 
            : 'pt-20 sm:pt-32 pb-10 sm:pb-16'
        ]">
          <!-- 搜索框 -->
          <div class="w-full max-w-2xl">
            <div class="relative group">
              <!-- 彩虹边框 -->
              <div class="absolute inset-0 rounded-full">
                <div class="absolute inset-0 rounded-full bg-gradient-to-r from-red-500 via-orange-500 via-yellow-500 via-green-500 via-blue-500 via-indigo-500 via-purple-500 via-pink-500 to-red-500 animate-liquid-rainbow p-[2px]">
                  <div class="w-full h-full bg-white dark:bg-slate-800 rounded-full"></div>
                </div>
              </div>
              
              <!-- 发光效果 -->
              <div class="absolute -inset-2 rounded-full bg-gradient-to-r from-red-400/30 via-orange-400/30 via-yellow-400/30 via-green-400/30 via-blue-400/30 via-indigo-400/30 via-purple-400/30 via-pink-400/30 to-red-400/30 animate-liquid-glow blur-xl opacity-60"></div>
              <div class="absolute -inset-1 rounded-full bg-gradient-to-r from-red-300/40 via-orange-300/40 via-yellow-300/40 via-green-300/40 via-blue-300/40 via-indigo-300/40 via-purple-300/40 via-pink-300/40 to-red-300/40 animate-liquid-glow-mid blur-lg opacity-40"></div>
              
              <input
                v-model="inputText"
                type="text"
                :placeholder="translations.searchInputPlaceholder"
                :class="[
                  'relative w-full px-5 sm:px-7 py-3 sm:py-4 text-base sm:text-lg rounded-full border-0 transition-all duration-500 outline-none glass-input z-10',
                  'placeholder:text-slate-400 dark:placeholder:text-slate-500',
                  'text-slate-900 dark:text-slate-100',
                  'shadow-lg',
                  inputFocused 
                    ? 'shadow-[0_0_40px_rgba(0,0,0,0.12)]' 
                    : 'shadow-[0_0_25px_rgba(0,0,0,0.08)]'
                ]"
                @keyup.enter="handleGenerate"
                @focus="handleInputFocus"
                @blur="handleInputBlur"
              />
              
              <!-- 搜索按钮和清除按钮 -->
              <div class="absolute right-2 sm:right-3 top-1/2 transform -translate-y-1/2 flex items-center space-x-1 sm:space-x-2 z-20">
                <button
                  v-if="inputText.trim() && !isLoading"
                  @click="handleClear"
                  class="p-1 sm:p-2 rounded-full glass-effect hover:bg-slate-100/80 dark:hover:bg-slate-700/80 text-slate-400 dark:text-slate-500 hover:text-slate-600 dark:hover:text-slate-300 transition-all duration-200"
                  title="Clear"
                >
                  <svg class="h-3.5 w-3.5 sm:h-4 sm:w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
                
                <button
                  @click="handleGenerate"
                  :disabled="!inputText.trim() || isLoading"
                  :class="[
                    'p-2 sm:p-2.5 rounded-full transition-all duration-300 transform flex items-center justify-center w-9 h-9 sm:w-11 sm:h-11',
                    isLoading
                      ? 'bg-blue-500 text-white scale-105 shadow-md cursor-wait'
                      : inputText.trim()
                        ? 'bg-blue-600 hover:bg-blue-700 text-white scale-100 hover:scale-105 shadow-lg hover:shadow-xl'
                        : 'bg-slate-100 dark:bg-slate-700 text-slate-400 dark:text-slate-500 cursor-not-allowed'
                  ]"
                  title="Generate Questions"
                >
                  <div v-if="isLoading" class="relative">
                    <svg class="animate-spin h-4 w-4 sm:h-5 sm:w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                      <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                  </div>
                  <svg v-else class="h-4 w-4 sm:h-5 sm:w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </button>
                
                <button
                  v-if="hasQuestions"
                  @click="navigateToRoundtable"
                  class="p-2 sm:p-2.5 rounded-full glass-effect hover:bg-slate-100/80 dark:hover:bg-slate-700/80 text-slate-600 dark:text-slate-300 hover:text-blue-600 dark:hover:text-blue-400 transition-all duration-200"
                  title="开始圆桌会议"
                >
                  <svg class="h-4 w-4 sm:h-5 sm:w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
          
          <!-- 问题分类筛选 -->
          <div v-if="hasQuestions" class="mt-6 sm:mt-8 w-full">
            <!-- 移动端：横向滚动 -->
            <div class="sm:hidden overflow-x-auto pb-2">
              <div class="flex space-x-2 px-1 min-w-max">
                <button
                  v-for="cat in categories"
                  :key="cat.value"
                  @click="handleCategoryChange(cat.value)"
                  :class="[
                    'px-2.5 py-1 rounded-full text-xs font-medium transition-all duration-200 whitespace-nowrap',
                    cat.color,
                    selectedCategory === cat.value 
                      ? 'ring-2 ring-offset-2 ring-blue-500 dark:ring-offset-slate-900' 
                      : 'hover:opacity-80'
                  ]"
                >
                  {{ cat.label }} ({{ getCategoryCount(cat.value) }})
                </button>
              </div>
            </div>
            
            <!-- 桌面端：居中换行 -->
            <div class="hidden sm:flex flex-wrap justify-center gap-1.5">
              <button
                v-for="cat in categories"
                :key="cat.value"
                @click="handleCategoryChange(cat.value)"
                :class="[
                  'px-3.5 py-1.5 rounded-full text-sm font-medium transition-all duration-200',
                  cat.color,
                  selectedCategory === cat.value 
                    ? 'ring-2 ring-offset-2 ring-blue-500 dark:ring-offset-slate-900' 
                    : 'hover:opacity-80'
                  ]"
              >
                {{ cat.label }} ({{ getCategoryCount(cat.value) }})
              </button>
            </div>
          </div>

          <!-- 错误信息 -->
          <div v-if="error" class="mt-4 text-red-500 dark:text-red-400 text-center text-sm px-4">
            {{ error }}
          </div>

          <!-- 问题列表 -->
          <div v-if="hasQuestions" class="mt-6 sm:mt-10 w-full">
            <!-- 移动端：单列布局 -->
            <div class="sm:hidden space-y-4">
              <QuestionCard 
                v-for="(question, index) in filteredQuestions" 
                :key="question.id || index" 
                :question="question" 
              />
            </div>
            
            <!-- 桌面端：网格布局 -->
            <div class="hidden sm:grid grid-cols-[repeat(auto-fit,minmax(280px,1fr))] gap-6">
              <QuestionCard 
                v-for="(question, index) in filteredQuestions" 
                :key="question.id || index" 
                :question="question" 
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 历史记录侧边栏 -->
    <HistoryPanel :show="showHistory" :history="history" @close="toggleHistory" @load-history="loadFromHistory" />

    <!-- 底部操作栏 -->
    <div class="fixed bottom-0 w-full bg-white dark:bg-slate-800 border-t border-slate-200 dark:border-slate-700 py-2.5 sm:py-3.5 px-3 sm:px-6 lg:px-8 shadow-lg z-30">
      <div class="container mx-auto max-w-4xl">
        <!-- 移动端：紧凑布局 -->
        <div class="sm:hidden flex justify-between items-center">
          <div class="flex items-center space-x-1.5">
            <ThemeToggle />
            <LanguageToggle />
          </div>
          <div class="flex items-center space-x-1.5">
            <button
              @click="toggleHistory"
              class="p-2 rounded-full glass-effect hover:bg-slate-100/80 dark:hover:bg-slate-700/80 text-slate-600 dark:text-slate-300 hover:text-blue-600 dark:hover:text-blue-400 transition-all duration-200"
              :title="language === 'zh' ? '历史记录' : 'History'"
            >
              <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </button>
            <ModelSettings />
          </div>
        </div>
        
        <!-- 桌面端：居中布局 -->
        <div class="hidden sm:flex justify-center items-center space-x-3">
          <ThemeToggle />
          <LanguageToggle />
          <button
            @click="toggleHistory"
            class="p-2 rounded-full glass-effect hover:bg-slate-100/80 dark:hover:bg-slate-700/80 text-slate-600 dark:text-slate-300 hover:text-blue-600 dark:hover:text-blue-400 transition-all duration-200 flex items-center space-x-2"
            :title="language === 'zh' ? '历史记录' : 'History'"
          >
            <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span class="hidden lg:inline">{{ language === 'zh' ? '历史' : 'History' }}</span>
          </button>
          <ModelSettings />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.glass-input {
  background-color: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.5);
  box-shadow: inset 0 0 10px rgba(255, 255, 255, 0.3), 0 0 20px rgba(0, 0, 0, 0.1);
}

.dark .glass-input {
  background-color: rgba(30, 41, 59, 0.8);
  border: 1px solid rgba(71, 85, 105, 0.5);
  box-shadow: inset 0 0 10px rgba(255, 255, 255, 0.1), 0 0 20px rgba(0, 0, 0, 0.2);
}

.glass-effect {
  background-color: rgba(255, 255, 255, 0.4);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.dark .glass-effect {
  background-color: rgba(30, 41, 59, 0.4);
  border: 1px solid rgba(71, 85, 105, 0.2);
}

@keyframes liquid-rainbow {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes liquid-glow {
  0% {
    filter: hue-rotate(0deg) blur(3px);
    opacity: 0.15;
  }
  50% {
    filter: hue-rotate(180deg) blur(4.5px);
    opacity: 0.2;
  }
  100% {
    filter: hue-rotate(360deg) blur(3px);
    opacity: 0.15;
  }
}

@keyframes liquid-glow-mid {
  0% {
    filter: hue-rotate(0deg) blur(2px);
    opacity: 0.1;
  }
  50% {
    filter: hue-rotate(180deg) blur(3px);
    opacity: 0.15;
  }
  100% {
    filter: hue-rotate(360deg) blur(2px);
    opacity: 0.1;
  }
}

.animate-liquid-rainbow {
  background-size: 400% 400%;
  animation: liquid-rainbow 60s ease infinite;
}

.animate-liquid-glow {
  animation: liquid-glow 40s ease infinite;
}

.animate-liquid-glow-mid {
  animation: liquid-glow-mid 48s ease-in-out infinite;
}

/* 移动端滚动条样式 */
.overflow-x-auto::-webkit-scrollbar {
  height: 2px;
}

.overflow-x-auto::-webkit-scrollbar-track {
  background: transparent;
}

.overflow-x-auto::-webkit-scrollbar-thumb {
  background: rgba(148, 163, 184, 0.3);
  border-radius: 1px;
}
</style>