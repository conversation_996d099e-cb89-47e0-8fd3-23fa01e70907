<script setup lang="ts">
import { RouterView } from 'vue-router'
import { theme } from './stores/theme'
import { language } from './stores/language'
import { watchEffect } from 'vue'

// Watch for language changes and update the lang attribute
watchEffect(() => {
  document.documentElement.lang = language.value
})

</script>

<template>
  <div class="min-h-screen bg-white dark:bg-slate-900 transition-colors duration-300">
    <RouterView />
  </div>
</template>

<style>
/* 可以添加一些全局样式，例如动画 */
</style>